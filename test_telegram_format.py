#!/usr/bin/env python3
"""
Test script for Telegram watchlist formatting
"""

import asyncio
import logging
from handlers.telegram.telegram_commands import TelegramCommandHandler

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_telegram_format():
    """Test the Telegram watchlist formatting"""
    try:
        # Create handler
        handler = TelegramCommandHandler()
        
        # Get watchlist data
        watchlist_data = await handler.market_service.get_watchlist_data()

        if not watchlist_data.get('success'):
            print(f"❌ Error getting watchlist data: {watchlist_data.get('error')}")
            return

        print("✅ Got watchlist data successfully")
        print(f"Data structure: {watchlist_data}")

        # Transform data to expected format
        if 'prices' in watchlist_data:
            # Convert to expected format
            transformed_data = {'data': {}}
            for symbol, price in watchlist_data['prices'].items():
                transformed_data['data'][symbol] = {
                    'price': price,
                    'daily_change_percent': 0,  # Mock data for test
                    'volume_24h': 1000000000   # Mock data for test
                }
            watchlist_data = transformed_data
        
        # Format message
        price_changes = {}  # Empty for test
        message = await handler.format_watchlist_message(watchlist_data, price_changes)
        
        print("\n" + "="*50)
        print("TELEGRAM WATCHLIST MESSAGE:")
        print("="*50)
        print(message)
        print("="*50)
        
        # Test message length
        print(f"\nMessage length: {len(message)} characters")
        if len(message) > 4096:
            print("⚠️  WARNING: Message exceeds Telegram limit (4096 chars)")
        else:
            print("✅ Message length is within Telegram limits")
            
    except Exception as e:
        logger.error(f"Error in test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_telegram_format())
